import numpy as np
import networkx as nx
import random
from collections import Counter
from typing import List, Set, Tuple, Dict, Optional
import matplotlib.pyplot as plt

from base_fun import IC, PRE, gen_graph


# ---------------------------
# 离散 NM（DM）各步骤函数
# ---------------------------

def node_score(G: nx.Graph, v: int) -> float:
    return float(G.degree(v))


def objective(G: nx.Graph, seed_set: Set[int], p: float, neighbors: Dict[int, List[int]], max_hop: int) -> float:
    # 适应度：直接使用 PRE（值越大越好）
    return PRE(G, seed_set, p, neighbors, max_hop=max_hop)


def evaluate_simplex(G: nx.Graph, simplex: List[Set[int]], p: float, neighbors: Dict[int, List[int]], max_hop: int) -> List[Tuple[float, Set[int]]]:
    # 计算单纯形内各顶点（种子集合）的适应度（越大越好）
    scored = [(objective(G, s, p, neighbors, max_hop), s) for s in simplex]
    scored.sort(key=lambda t: t[0], reverse=True)
    return scored


def initialize_simplex(G: nx.Graph, n: int, k: int, random_seed: Optional[int] = None) -> List[Set[int]]:
    if random_seed is not None:
        random.seed(random_seed)
        np.random.seed(random_seed)
    all_nodes: List[int] = list(G.nodes())
    if len(all_nodes) < k:
        raise ValueError("图的节点数量小于所需的种子集合大小 k")
    simplex: List[Set[int]] = []
    for _ in range(n + 1):
        cand = set(random.sample(all_nodes, k))
        simplex.append(cand)
    return simplex


# 基于节点度数的初始化（带随机扰动），返回 n+1 个解
# 每个解是大小为 k 的节点集合

def degree_initialization(G: nx.Graph, n: int, k: int, random_seed: Optional[int] = None) -> List[Set[int]]:
    if random_seed is not None:
        random.seed(random_seed)
        np.random.seed(random_seed)

    nodes: List[int] = list(G.nodes())
    if len(nodes) < k:
        raise ValueError("图的节点数量小于所需的种子集合大小 k")

    degree_sorted_nodes: List[int] = sorted(nodes, key=lambda node: G.degree(node), reverse=True)

    solutions: List[Set[int]] = []
    for _ in range(n + 1):
        position: List[int] = degree_sorted_nodes[:k]
        for i in range(k):
            if random.random() > 0.5:
                available_nodes = [node for node in nodes if node not in position]
                if available_nodes:
                    position[i] = random.choice(available_nodes)
        solutions.append(set(position))

    return solutions


def centroid_discrete(G: nx.Graph, simplex_sets: List[Set[int]], exclude_set: Set[int], k: int) -> Set[int]:
    # 计算除了最差解以外的“离散质心”（频率最高/度更高的节点优先）
    freq = Counter()
    for s in simplex_sets:
        if s is exclude_set:
            continue
        freq.update(s)
    candidates = sorted(freq.items(), key=lambda t: (t[1], node_score(G, t[0])), reverse=True)
    chosen = [v for v, _ in candidates[:k]]
    if len(chosen) < k:
        all_nodes: List[int] = list(G.nodes())
        remaining = [v for v in all_nodes if v not in chosen]
        remaining.sort(key=lambda v: node_score(G, v), reverse=True)
        chosen += remaining[: (k - len(chosen))]

    # 对最终选出的 k 个节点进行随机替换：对每个节点以 0.5 概率，用网络中不在当前集合的随机节点替换
    # 确保替换后的集合元素唯一且总数保持为 k
    try:
        import random as _rnd
        chosen_set: Set[int] = set(chosen[:k])
        all_nodes_set: Set[int] = set(G.nodes())
        available: Set[int] = all_nodes_set - chosen_set
        if available:
            chosen_list = list(chosen_set)
            for v in chosen_list:
                if _rnd.random() < 0.5 and available:
                    # 从未被选中的节点中随机挑一个替换 v
                    new_v = _rnd.choice(tuple(available))
                    chosen_set.remove(v)
                    chosen_set.add(new_v)
                    # 更新可用池，避免重复选入
                    available.discard(new_v)
                    available.add(v)
        chosen = list(chosen_set)
    except Exception:
        # 若出现异常（极少数边界情况），退回到原始选择
        pass

    return set(chosen[:k])


def worst_nodes(G: nx.Graph, X: Set[int], m: int) -> List[int]:
    # 在集合 X 中选出度最小的 m 个节点
    m = max(0, min(m, len(X)))
    return [v for v, _ in sorted(((v, node_score(G, v)) for v in X), key=lambda t: t[1])[:m]]


def top_nodes(G: nx.Graph, pool: Set[int], m: int, base: Set[int] | None = None) -> List[int]:
    # 在 pool 中选出度最大的 m 个节点（避免与 base 重复）
    base = base or set()
    avail = [v for v in pool if v not in base]
    avail.sort(key=lambda v: node_score(G, v), reverse=True)
    return avail[:max(0, m)]


def repair_size(G: nx.Graph, X: Set[int], k: int) -> Set[int]:
    # 调整集合大小到 k（若超出则丢弃度小节点，不足则补充度大节点）
    if len(X) > k:
        need_remove = len(X) - k
        to_drop = worst_nodes(G, X, need_remove)
        X = set(v for v in X if v not in to_drop)
    elif len(X) < k:
        need_add = k - len(X)
        all_nodes: Set[int] = set(G.nodes())
        adds = top_nodes(G, all_nodes - X, need_add)
        X = set(X) | set(adds)
    return X


# 反射：将“质心”相对最差解进行反射，尝试朝好的方向探索
def reflect_step(G: nx.Graph, x_c: Set[int], x_worst: Set[int], alpha: int, k: int, verbose: bool = False) -> Set[int]:
    D = x_c - x_worst
    W = set(worst_nodes(G, x_c, alpha))
    base = x_c - W
    chosen = set(top_nodes(G, D, len(W), base=base))
    x_r = base | chosen
    x_r = repair_size(G, x_r, k)
    if verbose:
        print(f"【反射】D大小={len(D)} 被替换数={len(W)} -> 反射解大小={len(x_r)}")
    return x_r


# 扩展：在反射成功的基础上进一步加大步长探索
def expand_step(G: nx.Graph, x_r: Set[int], D: Set[int], gamma: int, k: int, verbose: bool = False) -> Set[int]:
    Wp = set(worst_nodes(G, x_r, gamma))
    base = x_r - Wp
    chosen = set(top_nodes(G, D, len(Wp), base=base))
    x_e = base | chosen
    x_e = repair_size(G, x_e, k)
    if verbose:
        print(f"【扩展】被替换数={len(Wp)} -> 扩展解大小={len(x_e)}")
    return x_e


# 外部收缩：当反射优于最差解但不如次差解时，向质心方向小幅移动
def contract_outside_step(G: nx.Graph, x_c: Set[int], x_worst: Set[int], rho: int, k: int, verbose: bool = False) -> Set[int]:
    D = x_c - x_worst
    Wr = set(worst_nodes(G, x_c, rho))
    base = x_c - Wr
    chosen = set(top_nodes(G, D, len(Wr), base=base))
    x_co = base | chosen
    x_co = repair_size(G, x_co, k)
    if verbose:
        print(f"【外部收缩】被替换数={len(Wr)} -> 外收缩解大小={len(x_co)}")
    return x_co


# 内部收缩：当反射不优于最差解时，从最差解向质心方向靠拢
def contract_inside_step(G: nx.Graph, x_worst: Set[int], x_c: Set[int], rho: int, k: int, verbose: bool = False) -> Set[int]:
    D = x_c - x_worst
    Wr = set(worst_nodes(G, x_worst, rho))
    base = x_worst - Wr
    chosen = set(top_nodes(G, D, len(Wr), base=base))
    x_ci = base | chosen
    x_ci = repair_size(G, x_ci, k)
    if verbose:
        print(f"【内部收缩】被替换数={len(Wr)} -> 内收缩解大小={len(x_ci)}")
    return x_ci


# 回退（收缩）：以最优解为核心，压缩其他解以重新探索
def shrink_step(G: nx.Graph, scored_simplex: List[Tuple[float, Set[int]]], k: int, sigma: float, verbose: bool = False) -> List[Set[int]]:
    f_best, x_best = scored_simplex[0]
    new_simplex = [set(x_best)]
    keep = max(1, int(round(sigma * k)))
    for _, x_i in scored_simplex[1:]:
        top_keep = top_nodes(G, x_i, keep)
        x_shrink = set(x_best) | set(top_keep)
        x_shrink = repair_size(G, x_shrink, k)
        new_simplex.append(x_shrink)
    if verbose:
        print(f"【回退-收缩】保留比例≈{sigma} 实际保留节点数={keep} 新单纯形大小={len(new_simplex)}")
    return new_simplex

