# ==============================================================================
# 简单测试脚本 - 验证修改后的离散 Nelder-Mead 算法
# ==============================================================================

import numpy as np
import networkx as nx
import random
import time
from typing import List, Set, Tuple, Optional, Dict

# 自定义模块导入
from base_fun import IC, gen_graph
from NM_fun import (
    objective, evaluate_simplex, centroid_discrete,
    reflect_step, expand_step, contract_outside_step, contract_inside_step,
    degree_initialization, shrink_step
)

def create_test_graph():
    """创建一个简单的测试图"""
    G = nx.Graph()
    # 添加20个节点
    G.add_nodes_from(range(20))
    # 添加一些边
    edges = [(0,1), (1,2), (2,3), (3,4), (4,5), (5,6), (6,7), (7,8), (8,9),
             (0,10), (10,11), (11,12), (12,13), (13,14), (14,15), (15,16), (16,17), (17,18), (18,19),
             (1,10), (2,11), (3,12), (4,13), (5,14), (6,15), (7,16), (8,17), (9,18)]
    G.add_edges_from(edges)
    return G

def test_shrink_trigger():
    """测试收缩回退触发条件"""
    print("=== 测试收缩回退触发条件 ===")
    
    # 创建测试图
    G = create_test_graph()
    print(f"测试图：{G.number_of_nodes()}个节点，{G.number_of_edges()}条边")
    
    # 算法参数
    n = 5        # 单纯形维度
    k = 3        # 种子集合大小
    p = 0.1      # 传播概率
    gmax = 10    # 最大迭代次数
    alpha = 1    # 反射系数
    gamma = 2    # 扩展系数
    rho = 1      # 收缩系数
    sigma = 0.5  # 收缩回退比例
    max_hop = 3  # PRE 递推轮数
    
    # 设置随机种子
    random.seed(42)
    np.random.seed(42)
    
    # 预计算邻接列表
    neighbors: Dict[int, List[int]] = {v: list(G.neighbors(v)) for v in G.nodes()}
    
    # 初始化单纯形
    simplex = degree_initialization(G, n, k, random_seed=42)
    scored_simplex = evaluate_simplex(G, simplex, p, neighbors, max_hop)
    
    print(f"初始单纯形大小：{len(scored_simplex)}")
    init_scores = [f for f, _ in scored_simplex]
    print(f"初始适应度：{init_scores}")
    
    # 模拟一轮进化，重点测试收缩回退触发
    scored_simplex.sort(key=lambda t: t[0], reverse=True)
    f_best, x_best = scored_simplex[0]
    
    print(f"\n当前最优适应度：{f_best:.4f}")
    print(f"当前最优解：{sorted(x_best)}")
    
    # 预计算所有个体的集合
    all_sets = [s for _, s in scored_simplex]
    new_candidates: List[Tuple[float, Set[int]]] = []
    contract_failed_count = 0
    need_contract_count = 0
    
    # 对每个个体进行进化操作
    for j, (f_j, x_j) in enumerate(scored_simplex):
        print(f"\n--- 处理个体 {j}，适应度：{f_j:.4f} ---")
        
        # 计算排除自身后的离散质心
        x_c = centroid_discrete(G, all_sets, exclude_set=x_j, k=k)
        D = x_c - x_j
        
        print(f"质心：{sorted(x_c)}")
        print(f"差集D：{sorted(D)} (大小：{len(D)})")
        
        if len(D) == 0:
            print("D为空，跳过进化操作")
            candidate = (f_j, x_j)
        else:
            need_contract_count += 1
            
            # 反射操作
            x_r = reflect_step(G, x_c, x_j, alpha=alpha, k=k, verbose=False)
            f_r = objective(G, x_r, p, neighbors, max_hop)
            print(f"反射解：{sorted(x_r)}，适应度：{f_r:.4f}")
            
            # 计算阈值
            others_scores = [scored_simplex[i][0] for i in range(len(scored_simplex)) if i != j]
            f_threshold = min(others_scores) if others_scores else f_j
            print(f"阈值：{f_threshold:.4f}")
            
            if f_r > f_best:
                print("执行扩展操作")
                x_e = expand_step(G, x_r, D, gamma=gamma, k=k, verbose=False)
                f_e = objective(G, x_e, p, neighbors, max_hop)
                print(f"扩展解：{sorted(x_e)}，适应度：{f_e:.4f}")
                candidate = (f_e, x_e) if f_e > f_r else (f_r, x_r)
            elif f_r > f_threshold:
                print("接受反射结果")
                candidate = (f_r, x_r)
            else:
                print("执行收缩操作")
                contract_success = False
                if f_r > f_j:
                    print("外部收缩")
                    x_co = contract_outside_step(G, x_c, x_j, rho=rho, k=k, verbose=False)
                    f_co = objective(G, x_co, p, neighbors, max_hop)
                    print(f"外收缩解：{sorted(x_co)}，适应度：{f_co:.4f}")
                    if f_co > f_j:
                        candidate = (f_co, x_co)
                        contract_success = True
                        print("外部收缩成功")
                    else:
                        candidate = (f_j, x_j)
                        print("外部收缩失败")
                else:
                    print("内部收缩")
                    x_ci = contract_inside_step(G, x_j, x_c, rho=rho, k=k, verbose=False)
                    f_ci = objective(G, x_ci, p, neighbors, max_hop)
                    print(f"内收缩解：{sorted(x_ci)}，适应度：{f_ci:.4f}")
                    if f_ci > f_j:
                        candidate = (f_ci, x_ci)
                        contract_success = True
                        print("内部收缩成功")
                    else:
                        candidate = (f_j, x_j)
                        print("内部收缩失败")
                
                if not contract_success:
                    contract_failed_count += 1
        
        new_candidates.append(candidate)
        print(f"最终候选：适应度={candidate[0]:.4f}")
    
    print(f"\n=== 收缩统计 ===")
    print(f"需要收缩的个体数：{need_contract_count}")
    print(f"收缩失败的个体数：{contract_failed_count}")
    
    # 检测是否触发回退
    if need_contract_count > 0 and contract_failed_count == need_contract_count:
        print("*** 触发收缩回退操作 ***")
        new_simplex_sets = shrink_step(G, scored_simplex, k, sigma, verbose=True)
        print(f"回退后单纯形大小：{len(new_simplex_sets)}")
        for i, s in enumerate(new_simplex_sets):
            f_s = objective(G, s, p, neighbors, max_hop)
            print(f"回退解{i}：{sorted(s)}，适应度：{f_s:.4f}")
    else:
        print("未触发收缩回退")
        print("正常更新单纯形")

if __name__ == "__main__":
    test_shrink_trigger()
