import numpy as np
import networkx as nx
import random
from collections import Counter
from typing import List, Set, Tuple, Dict
import matplotlib.pyplot as plt


def gen_graph(filename: str) -> nx.Graph:
    """
    从文件加载图形数据并生成无向图
    
    Args:
        filename: 包含边列表的文件路径
        
    Returns:
        nx.Graph: 生成的无向图对象
        
    Raises:
        RuntimeError: 当图加载失败时
    """
    try:
        G = nx.Graph()  # 创建一个空的无向图
        edges_data = np.loadtxt(filename, skiprows=1, usecols=[0, 1])  # 读取边数据
        edges = [(int(u), int(v)) for u, v in edges_data]
        G.add_edges_from(edges)  # 将边添加到图中
        return G  # 返回生成的图
    except Exception as e:
        raise RuntimeError(f"加载图形错误: {e}")
    

# 影响力传播模拟 (IC模型)
def IC(g, seed, p, mc=1000):
    seed = set(seed)  # 转换为集合，避免重复元素
    influence = []
    neighbors_of = g.neighbors  # 绑定局部以减少属性查找
    rand = np.random.random
    for _ in range(mc):
        new_active, last_active = set(seed), set(seed)  # 使用集合来去重
        while new_active:
            new_ones = set()
            for node in new_active:
                for neighbor in neighbors_of(node):  # 直接迭代邻居，避免 list 转换
                    if rand() < p:
                        new_ones.add(neighbor)
            new_active = new_ones - last_active
            last_active.update(new_active)
        influence.append(len(last_active))  # 记录激活的总节点数
    return np.mean(influence)  # 返回平均影响力


# ---------------------------
# PRE 近似影响力估计
# ---------------------------

def PRE(G: nx.Graph, S: Set[int], p: float, neighbors: Dict[int, List[int]], max_hop: int = 5) -> float:
    """
    PRE近似影响力估计（全节点递推）
    使用原始的连乘计算更新
    Args:
        S: 种子节点集合
        G: 网络图对象（NetworkX Graph）
        p: 传播概率
        neighbors: 每个节点的邻居节点信息，字典格式 {node: [neighbors]}
        max_hop: 最大递推轮数
    Returns:
        float: 估计影响力值
    """
    S = set(S)
    # 缓存节点列表到图对象，避免在频繁调用中重复构造
    if not hasattr(G, "_node_list_cache"):
        setattr(G, "_node_list_cache", list(G.nodes()))
    nodes: List[int] = getattr(G, "_node_list_cache")

    P: dict[int, float] = {v: 1.0 if v in S else 0.0 for v in nodes}

    neighbors_get = neighbors.get
    S_contains = S.__contains__
    for _ in range(max_hop):
        new_P: dict[int, float] = {}
        # 先确保种子为 1
        for sv in S:
            new_P[sv] = 1.0
        # 更新非种子
        for v in nodes:
            if S_contains(v):
                continue
            parents = neighbors_get(v, [])
            if not parents:
                new_P[v] = 0.0
                continue
            prob_not = 1.0  # 连乘的“未被激活”概率
            P_get = P.get
            one_minus = 1.0 - p
            for u in parents:
                Pu = P_get(u, 0.0)
                # factor = 1 - p * Pu，限制下界避免下溢
                factor = 1.0 - p * Pu
                if factor < 1e-20:
                    factor = 1e-20
                prob_not *= factor
            new_P[v] = 1.0 - prob_not
        P = new_P

    return float(sum(P.values()))  # 估计的总影响力（期望激活节点数）

